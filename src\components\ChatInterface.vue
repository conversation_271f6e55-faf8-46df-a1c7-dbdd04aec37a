<script setup>
import { ref } from 'vue';

const props = defineProps({
  messages: {
    type: Array,
    default: () => []
  }
});

const emits = defineEmits(['send-message']);

const newMessage = ref('');

function sendMessage() {
  if (!newMessage.value.trim()) return;
  console.log('ChatInterface: Sending message:', newMessage.value);
  emits('send-message', newMessage.value);
  newMessage.value = '';
}

function handleKeydown(event) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
}

// Format message content with markdown-like syntax
function formatMessage(content) {
  if (!content) return '';
  
  // Replace *text* with italic text
  let formattedContent = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
  
  return formattedContent;
}
</script>

<template>
  <div class="chat-interface">
    <h3>Chat</h3>
    
    <div class="chat-messages">
      <div v-if="!messages || messages.length === 0" class="empty-chat">
        <p>Envía un mensaje a tu mascota para comenzar a chatear</p>
      </div>

      <div v-for="message in messages || []" :key="message.id" class="chat-message" :class="message.sender">
        <div class="message-content" v-html="formatMessage(message.content)"></div>
        <div class="message-time">
          {{ new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) }}
        </div>
      </div>
    </div>
    
    <div class="chat-input">
      <textarea 
        v-model="newMessage"
        placeholder="Escribe un mensaje a tu mascota..."
        @keydown="handleKeydown"
      ></textarea>
      <button class="send-btn" @click="sendMessage" :disabled="!newMessage.trim()">
        Enviar
      </button>
    </div>
  </div>
</template>

<style scoped>
.chat-interface {
  background-color: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  display: flex;
  flex-direction: column;
  height: 100%;
}

h3 {
  margin-bottom: var(--spacing-md);
  color: var(--neutral-800);
}

.chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column-reverse;
  gap: var(--spacing-sm);
  padding-right: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  max-height: 300px;
}

.empty-chat {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: var(--neutral-500);
  font-style: italic;
  text-align: center;
}

.chat-message {
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  max-width: 80%;
  margin-bottom: var(--spacing-sm);
  position: relative;
}

.chat-message.user {
  background-color: var(--primary-light);
  align-self: flex-end;
  border-bottom-right-radius: 0;
}

.chat-message.pet {
  background-color: var(--neutral-200);
  align-self: flex-start;
  border-bottom-left-radius: 0;
}

.message-content {
  word-break: break-word;
}

.message-content em {
  font-style: italic;
  color: var(--neutral-600);
}

.message-time {
  font-size: var(--font-size-xs);
  color: var(--neutral-600);
  margin-top: var(--spacing-xs);
  text-align: right;
}

.chat-input {
  display: flex;
  gap: var(--spacing-sm);
}

textarea {
  flex-grow: 1;
  resize: none;
  height: 60px;
  padding: var(--spacing-sm);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: var(--font-size-sm);
  transition: border-color var(--transition-normal);
}

textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.send-btn {
  align-self: flex-end;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  transition: background-color var(--transition-normal);
}

.send-btn:hover {
  background-color: var(--primary-dark);
}

.send-btn:disabled {
  background-color: var(--neutral-400);
  cursor: not-allowed;
}

/* Scrollbar styling */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: var(--neutral-200);
  border-radius: var(--radius-full);
}

.chat-messages::-webkit-scrollbar-thumb {
  background: var(--neutral-400);
  border-radius: var(--radius-full);
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-500);
}
</style>