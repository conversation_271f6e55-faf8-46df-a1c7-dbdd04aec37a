<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/authStore.js';
import { usePetStore } from '../stores/petStore.js';
import { PetType } from '../stores/petTypes.js';

const router = useRouter();
const authStore = useAuthStore();
const petStore = usePetStore();

const petName = ref('');
const selectedType = ref(PetType.CAT);
const errorMessage = ref('');
const isLoading = ref(false);

if (!authStore.user) {
  router.push('/login');
}

async function createPet() {
  if (!petName.value) {
    errorMessage.value = 'Por favor ingresa un nombre para tu mascota';
    return;
  }
  if (!authStore.user) {
    router.push('/login');
    return;
  }
  isLoading.value = true;
  errorMessage.value = '';
  try {
    await petStore.createPet(petName.value, selectedType.value, authStore.user.id);
    // Espera a que la mascota esté realmente disponible antes de redirigir
    await petStore.getPetByOwnerId(authStore.user.id);
    router.push('/pet');
  } catch (error) {
    errorMessage.value = error.message || 'Error al crear la mascota';
  } finally {
    isLoading.value = false;
  }
}

function selectPetType(type) {
  selectedType.value = type;
}

function goBack() {
  router.push('/');
}
</script>

<template>
  <div class="create-pet-container">
    <div class="container">
      <div class="create-pet-card">
        <div class="card-header">
          <button class="back-button" @click="goBack">
            ← Volver
          </button>
          <h1>Crea tu mascota virtual</h1>
        </div>
        
        <div class="pet-form">
          <div class="form-group">
            <label for="petName">Nombre de tu mascota</label>
            <input 
              id="petName" 
              type="text" 
              v-model="petName"
              placeholder="Ej: Fluffy, Max, Luna..."
              required
            />
          </div>
          
          <div class="pet-type-selection">
            <h3>Selecciona un tipo de mascota</h3>
            
            <div class="pet-types">
              <div 
                class="pet-type-option" 
                :class="{ 'selected': selectedType === PetType.CAT }"
                @click="selectPetType(PetType.CAT)"
              >
                <img src="https://images.pexels.com/photos/45201/kitty-cat-kitten-pet-45201.jpeg" alt="Cat" />
                <span>Gato</span>
              </div>
              
              <div 
                class="pet-type-option" 
                :class="{ 'selected': selectedType === PetType.DOG }"
                @click="selectPetType(PetType.DOG)"
              >
                <img src="https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg" alt="Dog" />
                <span>Perro</span>
              </div>
              
              <div 
                class="pet-type-option" 
                :class="{ 'selected': selectedType === PetType.BUNNY }"
                @click="selectPetType(PetType.BUNNY)"
              >
                <img src="https://images.pexels.com/photos/326012/pexels-photo-326012.jpeg" alt="Bunny" />
                <span>Conejo</span>
              </div>
            </div>
          </div>
          
          <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>
          
          <button 
            class="btn-primary create-btn"
            @click="createPet"
            :disabled="isLoading"
          >
            <span v-if="isLoading">Creando...</span>
            <span v-else>¡Crear mi mascota!</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.create-pet-container {
  min-height: 100vh;
  background-color: var(--neutral-200);
  padding: var(--spacing-xl) 0;
}

.create-pet-card {
  background-color: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  max-width: 600px;
  margin: 0 auto;
}

.card-header {
  position: relative;
  margin-bottom: var(--spacing-xl);
  text-align: center;
}

.back-button {
  position: absolute;
  left: 0;
  top: 0;
  background: none;
  color: var(--primary-color);
  padding: 0;
}

.back-button:hover {
  color: var(--primary-dark);
}

.pet-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.form-group label {
  font-weight: var(--font-weight-medium);
  color: var(--neutral-700);
}

.pet-type-selection h3 {
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.pet-types {
  display: flex;
  justify-content: space-around;
  gap: var(--spacing-md);
}

.pet-type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
}

.pet-type-option img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 50%;
  border: 3px solid var(--neutral-300);
  transition: all var(--transition-normal);
}

.pet-type-option.selected img {
  border-color: var(--primary-color);
}

.pet-type-option:hover img {
  transform: scale(1.05);
  border-color: var(--primary-light);
}

.pet-type-option.selected {
  background-color: var(--primary-light);
}

.error-message {
  color: var(--error-color);
  font-size: var(--font-size-sm);
  text-align: center;
}

.create-btn {
  padding: var(--spacing-md);
  font-size: var(--font-size-lg);
}

.create-btn:disabled {
  background-color: var(--neutral-400);
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .pet-types {
    flex-direction: column;
  }
  
  .pet-type-option {
    flex-direction: row;
    justify-content: flex-start;
    width: 100%;
  }
  
  .pet-type-option img {
    width: 60px;
    height: 60px;
  }
}
</style>